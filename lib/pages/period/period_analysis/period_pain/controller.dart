import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';

class PeriodPainController extends GetxController {
  PeriodPainController();

  List<int> painLevels = [];
  List<String> dayLabels = [];

  List<PeriodItemModel> periodModelItems = [];
  List<PeriodItemModel> tempList = [];
  Map<String, List<FourType>> fourTypeMap = {};
  final int _pageSize = 10;

  @override
  void onReady() {
    super.onReady();
    
    update(["period_pain"]);
  }
}
